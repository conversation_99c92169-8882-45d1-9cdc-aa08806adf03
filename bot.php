<?php
ob_start();
error_reporting(0);
date_default_timezone_set('Asia/Tehran');
// input your information
$Token = '8101377108:AAEcJlRMUDQZvoJkruQruRRMHhaZLkEv1N4'; // Token
$Admin = 2038958340; // Admin user id
$UsernameBot = "@hajamir23_bot"; // Username bot with @
define('API_KEY',$Token);

// Language texts
$texts = [
    'fa' => [
        'welcome' => "سلام {name} 👋\n\nبه ربات نجوا گرام خوش آمدید!\n\nبا استفاده از این ربات شما و دوستانتان می توانید پیام هارا ناشناس ارسال کنید.\n\n<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
        'send_Unknown' => '💬 بخش نجوا',
        'private_message' => '✨ پیام خصوصی',
        'help' => '📚 راهنما',
        'support' => '☎️ پشتیبانی',
        'language' => '🇮🇷 زبان | lng',
        'privacy' => '👀 حریم خصوصی',
        'anonymous_message' => '🎩 پیام ناشناس',
        'send_instruction' => "✨ ارسال نجوا خصوصی\n\n✱ برای ارسال نجوا (پیام خصوصی) به شخص مورد نظر شما میتوانید با استفاده از موارد زیر اقدام کنید :\n\n1. فوروارد یک پیام از کاربر (در صورتی که ربات را استارت کرده باشد).\n\n2. ارسال آیدی عددی کاربر (اگر نمیدانید ایدی عددی چیست به قسمت راهنما رجوع کنید).\n\n3. ارسال یوزرنیم کاربر (مخصوص مواقعی که دسترسی به پیام یا ایدی شخص مورد نظر ندارید و یوزرنیم فقط جهت تگ کردن کاربر است).\n\n<blockquote>🤖 حال یکی از موارد بالا را انتخاب کنید و ارسال کنید.</blockquote>",
        'private_instruction' => "✨ ارسال نجوا خصوصی\n\n✱ برای ارسال نجوا (پیام خصوصی) به شخص مورد نظر شما میتوانید با استفاده از موارد زیر اقدام کنید :\n\n1. فوروارد یک پیام از کاربر (در صورتی که ربات را استارت کرده باشد).\n\n2. ارسال آیدی عددی کاربر (اگر نمیدانید ایدی عددی چیست به قسمت راهنما رجوع کنید).\n\n3. ارسال یوزرنیم کاربر (مخصوص مواقعی که دسترسی به پیام یا ایدی شخص مورد نظر ندارید و یوزرنیم فقط جهت تگ کردن کاربر است).\n\n<blockquote>🤖 حال یکی از موارد بالا را انتخاب کنید و ارسال کنید.</blockquote>",
        'cancel' => 'لغو عملیات',
        'help_text' => "📚 راهنما\n\nبرای استفاده از ربات به صورت از راه دور شما میتوانید از دو روش اقدام کنید 👇\n\n1. جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Whispergram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس یوزرنیم گیرنده قرار دهید.\nمثال :\n<code>@Whispergram_Bot سلام @Ali</code>\n\n2.جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Whispergram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس ایدی عددی گیرنده قرار دهید.\nمثال :\n<code>@Whispergram_Bot سلام USER_ID(آیدی عددی فرد)</code>",
        'view_example' => '👀 مشاهده نمونه',
        'main_menu' => '🔙 بازگشت',
        'user_not_found' => "اوپس 😲\n\nاطلاعات کاربر دریافت نشد !\nاز او بخواهید ربات را استارت بزند سپس مجدد تلاش کنید ☹️",
        'user_info' => "✨ ارسال نجوا خصوصی\n\n✱ کاربر گرامی جهت ارسال نجوا خصوصی به یوزرنیمی که وارد کردید از دکمه زیر استفاده کنید:",
        'send_Unknown_to' => "ارسال نجوا به {name}",
        'not_your_message' => "❌ این نجوا برای شما ارسال نشده است!",
        'you_have_message' => "💬 نجوا جدیدی ارسال شده است.\n\nکاربر [ {user} ] شما یک پیام از طرف [ {sender} ] دارید.",
        'show_message' => "👀 مشاهده",
        'send_Unknown_here' => "برای ارسال نجوا اینجا ضربه بزنید ❗️",
        'send_secret_to' => "ارسال نجوا (پیام مخفی) به {user}\nاز @ در متن خود استفاده نکنید !",
        'send_secret_to_user' => "ارسال نجوا (پیام مخفی) به {name}\nاز # در متن خود استفاده نکنید !",
        'select_language' => "لطفاً زبان مورد نظر خود را انتخاب کنید:\nPlease select your preferred language:",
        'persian' => "🇮🇷 فارسی",
        'english' => "🇺🇸 English",
        'support_text' => "🤳 پشتیبانی\n\n✱ برای ارتباط با پشتیبانی و گزارش مشکلات می‌توانید از راه‌های زیر اقدام کنید:\n\n🛃 آیدی پشتیبانی: @Sia32vosh\n\n⏰ ساعات پاسخگویی: 9 صبح تا 9 شب\n\n<blockquote>لطفاً مشکل خود را به صورت کامل شرح دهید تا بتوانیم بهترین کمک را به شما ارائه دهیم.</blockquote>",
        'privacy_text_active' => "👀 تنظیمات حریم‌خصوصی\n\n<b>✱ وضعیت فعلی: فعال ✅</b>\n\n✱ با فعال کردن حریم خصوصی، هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.\n\n<blockquote>⚠️ توجه: در صورت فعال بودن حریم خصوصی، تمام پیام‌های ناشناس ارسالی به شما مسدود خواهد شد.</blockquote>",
        'privacy_text_inactive' => "👀 تنظیمات حریم‌خصوصی\n\n<b>✱ وضعیت فعلی: غیرفعال ❌</b>\n\n✱ با غیرفعال کردن حریم خصوصی، همه می‌توانند برای شما پیام ناشناس ارسال کنند.\n\n<blockquote>⚠️ توجه: در صورت غیرفعال بودن حریم خصوصی، تمام پیام‌های ناشناس به شما ارسال خواهد شد.</blockquote>",
        'disable_privacy' => '❌ غیرفعال کردن',
        'enable_privacy' => '✅ فعال کردن',
        'privacy_disabled' => 'حریم خصوصی غیرفعال شد ❌',
        'privacy_enabled' => 'حریم خصوصی فعال شد ✅',
        'anonymous_text' => "🎩 پیام ناشناس\n\n✱ پیام های ناشناس در گروه ها کاربرد دارد و شما میتوانید به صورت ناشناس در گروه برای تمامی افراد یا فردی خاص پیام ارسال کنید.\n\n✱ جهت ارسال پیام ابتدا یوزرنیم آن گروه را وارد کنید:",
        'group_not_found' => "❌ خطا!\n\nربات در این گروه عضو نیست یا گروه وجود ندارد.\nلطفاً ابتدا ربات را به گروه اضافه کنید و سپس مجدد تلاش کنید.",
        'group_found' => "✅ گروه پیدا شد!\n\n✱ یوزرنیم گروه: {group}\n✱ نام گروه: {group_title}\n\n✱ حالا پیام خود را بنویسید:",
        'message_received' => "📝 پیام دریافت شد!\n\n✱ حالا انتخاب کنید پیام برای چه کسی ارسال شود:",
        'send_to_all' => '👥 ارسال برای همه',
        'send_to_specific' => '👤 ارسال برای فرد خاص',
        'enter_username' => "👤 ارسال برای فرد خاص\n\nیوزرنیم فرد مورد نظر را وارد کنید:",
        'message_sent' => "✅ پیام ناشناس با موفقیت ارسال شد!\n\n✱ پیام شما کاملا ناشناس به گروه تعیین شده ارسال گردید.",
        'send_error' => "❌ خطا در ارسال پیام!\n\nممکن است ربات دسترسی لازم را نداشته باشد.",
        'anonymous_group_message' => "🎭 پیام ناشناس برای {target}:\n\n✱ جهت مشاهده پیام بر روی دکمه زیر کلیک کنید.\n✱ کاربری به طور ناشناس این پیام را از ربات ارسال کرده است.\n✱ مشاهده فقط برای کاربر تعیین شده ممکن است.",
        'anonymous_group_all' => "🎭 پیام ناشناس برای همه:\n\n✱ جهت مشاهده پیام بر روی دکمه زیر کلیک کنید.\n✱ کاربری به طور ناشناس این پیام را از ربات ارسال کرده است.\n✱ همه می‌توانند این پیام را مشاهده کنند.",
        'view_message' => '👁 مشاهده پیام',
        'not_for_you' => "این پیام برای شما نیست! 🚫",
        'anonymous_Whisper_message' => "💬 نجوا بی نام\n\n✱ کاربر [ {sender} ] یک نجوا به کاربری ناشناس ارسال کرد.",
        'Whisper_main' => "💬 بخش نجوا\n\n✱ به بخش نجوا خوش آمدید! از اینجا می‌توانید تنظیمات خود را مدیریت کنید یا راهنما را مطالعه کنید.",
        'settings' => '⚙️ تنظیمات',
        'guide' => '📖 راهنما',
        'Whisper_settings_text' => "⚙️ تنظیمات نجوا\n\n✱ از این بخش می‌توانید تنظیمات مربوط به نجوا را مدیریت کنید:",
        'read_notification' => '👁 اعلان خوانده شدن نجوا',
        'receive_notification' => '🔔 اعلان دریافت نجوا',
        'anonymous_Whisper' => '👀 نجوای بی نام',
        'disposable_Whisper' => '🗄 نجوا یکبار مصرف',
        'setting_enabled' => 'تنظیمات فعال شد ✅',
        'setting_disabled' => 'تنظیمات غیرفعال شد ❌',
        'message_already_viewed' => '👁‍🗨 شما قبلاً این نجوا را مشاهده کرده‌اید! نجوای یکبار مصرف فقط یک بار قابل مشاهده است.',
        'message_read_by' => "💬 نجوا جدیدی ارسال شده است.\n\nکاربر [ {user} ] شما یک پیام از طرف [ {sender} ] دارید.\n\n✅ این نجوا توسط [ {reader} ] خوانده شد.",
        'message_read_by_anonymous' => "💬 نجوا بی نام\n\n✱ کاربر [ {sender} ] یک نجوا به کاربری ناشناس ارسال کرد.\n\n✅ این نجوا توسط کاربر ناشناس تعیین شده خوانده شده است.",
        'main_menu' => '🏠 منوی اصلی',
        'ads' => '📢 تبلیغات',
        'guide' => '📖 راهنما',
        'ads_text' => "📢 تبلیغات ربات\n\nبرای تبلیغات در ربات با ادمین تماس بگیرید.",
        'guide_text' => "📖 راهنمای استفاده از ربات\n\nبرای ارسال نجوا:\n1. یوزرنیم مقصد را بنویسید\n2. پیام خود را تایپ کنید\n3. ارسال کنید"
    ],
    'en' => [
        'welcome' => "Hello {name} 👋\n\nWelcome to Unknown Gram bot!\n\nUsing this bot, you and your friends can send messages anonymously.\n\n<blockquote>📚 Please read the bot usage steps through the help button or /help command.</blockquote>",
        'send_Unknown' => '💬 Unknown Section',
        'private_message' => '✨ Private Message',
        'help' => '📚 Help',
        'support' => '☎️ Support',
        'language' => '🇺🇸 زبان | lng',
        'privacy' => '👀 Privacy',
        'anonymous_message' => '🎩 Anonymous Message',
        'send_instruction' => "✨ Send Private Whisper\n\n✱ To send Whisper (private message) to your desired person, you can use the following methods:\n\n1. Forward a message from the user (if they have started the bot).\n\n2. Send the user's numeric ID (if you don't know what numeric ID is, refer to the help section).\n\n3. Send the user's username (for cases where you don't have access to the message or ID of the desired person and the username is only for tagging the user).\n\n<blockquote>🤖 Now select and send one of the above options.</blockquote>",
        'private_instruction' => "✨ Send Private Whisper\n\n✱ To send Whisper (private message) to your desired person, you can use the following methods:\n\n1. Forward a message from the user (if they have started the bot).\n\n2. Send the user's numeric ID (if you don't know what numeric ID is, refer to the help section).\n\n3. Send the user's username (for cases where you don't have access to the message or ID of the desired person and the username is only for tagging the user).\n\n<blockquote>🤖 Now select and send one of the above options.</blockquote>",
        'cancel' => 'Cancel Operation',
        'help_text' => "📚 Help\n\nTo use the bot remotely, you can use two methods 👇\n\n1. To send, you must use inline. Just type the bot username ( @Whispergram_Bot ) in the chat first, then a space, then the text, then the recipient's username.\nExample:\n<code>@Whispergram_Bot Hello @Ali</code>\n\n2. To send, you must use inline. Just type the bot username ( @Whispergram_Bot ) in the chat first, then a space, then the text, then the recipient's numeric ID.\nExample:\n<code>@Whispergram_Bot Hello USER_ID(person's numeric ID)</code>",
        'view_example' => '👀 View Example',
        'main_menu' => '🔙 Back',
        'user_not_found' => "Oops 😲\n\nUser information not received!\nAsk them to start the bot and try again ☹️",
        'user_info' => "✨ Send Private Whisper\n\n✱ Dear user, to send a private Whisper to the username you entered, use the button below:",
        'send_Unknown_to' => "Send Unknown to {name}",
        'not_your_message' => "❌ This whisper was not sent to you!",
        'you_have_message' => "💬 A new whisper has been sent.\n\nUser [ {user} ] you have a message from [ {sender} ].",
        'show_message' => "👀 View",
        'send_Unknown_here' => "Click here to send Unknown ❗️",
        'send_secret_to' => "Send Unknown (secret message) to {user}\nDon't use @ in your text!",
        'send_secret_to_user' => "Send Unknown (secret message) to {name}\nDon't use # in your text!",
        'select_language' => "Please select your preferred language:\nلطفاً زبان مورد نظر خود را انتخاب کنید:",
        'persian' => "🇮🇷 فارسی",
        'english' => "🇺🇸 English",
        'support_text' => "🤳 Support\n\n✱ To contact support and report issues, you can use the following methods:\n\n🛃 Support ID: @Sia32vosh\n\n⏰ Response hours: 9 AM to 9 PM\n\n<blockquote>Please describe your problem completely so we can provide you with the best help.</blockquote>",
        'privacy_text_active' => "👀 Privacy Settings\n\n<b>✱ Current status: Active ✅</b>\n\n✱ By enabling privacy, no one can send you anonymous messages.\n\n<blockquote>⚠️ Note: If privacy is enabled, all anonymous messages sent to you will be blocked.</blockquote>",
        'privacy_text_inactive' => "👀 Privacy Settings\n\n<b>✱ Current status: Inactive ❌</b>\n\n✱ By disabling privacy, everyone can send you anonymous messages.\n\n<blockquote>⚠️ Note: If privacy is disabled, all anonymous messages will be sent to you.</blockquote>",
        'disable_privacy' => '❌ Disable',
        'enable_privacy' => '✅ Enable',
        'privacy_disabled' => 'Privacy disabled ❌',
        'privacy_enabled' => 'Privacy enabled ✅',
        'anonymous_text' => "🎩 Anonymous Message\n\n✱ Anonymous messages are useful in groups and you can send messages anonymously in the group to all members or a specific person.\n\n✱ To send a message, first enter the username of that group:",
        'group_not_found' => "❌ Error!\n\nThe bot is not a member of this group or the group does not exist.\nPlease add the bot to the group first and then try again.",
        'group_found' => "✅ Group found!\n\n✱ Group username: {group}\n✱ Group name: {group_title}\n\n✱ Now write your message text:",
        'message_received' => "📝 Message received!\n\n✱ Now choose who the message should be sent to:",
        'send_to_all' => '👥 Send to All',
        'send_to_specific' => '👤 Send to Specific Person',
        'enter_username' => "👤 Send to Specific Person\n\nEnter the username of the desired person:",
        'message_sent' => "✅ Anonymous message sent successfully!\n\n✱ Your message has been sent completely anonymously to the specified group.",
        'send_error' => "❌ Error sending message!\n\nThe bot may not have the necessary permissions.",
        'anonymous_group_message' => "🎭 Anonymous message for {target}:\n\n✱ Click the button below to view the message.\n✱ A user has anonymously sent this message from the bot.\n✱ Viewing is only possible for the specified user.",
        'anonymous_group_all' => "🎭 Anonymous message for everyone:\n\n✱ Click the button below to view the message.\n✱ A user has anonymously sent this message from the bot.\n✱ Everyone can view this message.",
        'view_message' => '👁 View Message',
        'not_for_you' => "This message is not for you! 🚫",
        'anonymous_Whisper_message' => "💬 Anonymous Whisper\n\n✱ User [ {sender} ] sent a Whisper to an anonymous user.",
        'Whisper_main' => "💬 Whisper Section\n\n✱ Welcome to the Whisper section! From here you can manage your settings or read the guide.",
        'settings' => '⚙️ Settings',
        'guide' => '📖 Guide',
        'Whisper_settings_text' => "⚙️ Whisper Settings\n\n✱ From this section you can manage settings related to Whisper:",
        'read_notification' => '👁 Read notification',
        'receive_notification' => '🔔 Receive notification',
        'anonymous_Whisper' => '👀 Anonymous Whisper',
        'disposable_Whisper' => '🗄 Disposable Whisper',
        'setting_enabled' => 'Setting enabled ✅',
        'setting_disabled' => 'Setting disabled ❌',
        'message_already_viewed' => 'You have already viewed this whisper! 👁‍🗨\n\nDisposable whispers can only be viewed once.',
        'message_read_by' => "💬 A new whisper has been sent.\n\nUser [ {user} ] you have a message from [ {sender} ].\n\n✅ This whisper was read by [ {reader} ].",
        'message_read_by_anonymous' => "💬 Anonymous Whisper\n\n✱ User [ {sender} ] sent a whisper to an anonymous user.\n\n✅ This whisper has been read by the designated anonymous user.",
        'main_menu' => '🏠 Main Menu',
        'ads' => '📢 Ads',
        'guide' => '📖 Guide',
        'ads_text' => "📢 Bot Advertisements\n\nFor advertising in the bot, contact the admin.",
        'guide_text' => "📖 Bot Usage Guide\n\nTo send a whisper:\n1. Write the target username\n2. Type your message\n3. Send it"
    ]
];

function sheikh($method,$datas=[]){
    $url = "https://api.telegram.org/bot".API_KEY."/".$method;
    $ch = curl_init();
    curl_setopt($ch,CURLOPT_URL,$url);
    curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
    curl_setopt($ch,CURLOPT_POSTFIELDS,$datas);
    $res = curl_exec($ch);
    if(curl_error($ch)){
        var_dump(curl_error($ch));
    }else{
        return json_decode($res);
    }
}
function step($address,$data){
	$user = json_decode(file_get_contents($address),true);
	$user["step"]="$data";
	$user = json_encode($user,true);
	file_put_contents($address,$user);
}
function set_language($address,$lang){
	$user = json_decode(file_get_contents($address),true);
	$user["language"]="$lang";
	$user = json_encode($user,true);
	file_put_contents($address,$user);
}
function get_language($address){
	if(file_exists($address)){
		$user = json_decode(file_get_contents($address),true);
		return isset($user["language"]) ? $user["language"] : "fa";
	}
	return "fa";
}
function set_privacy($address,$privacy){
	$user = json_decode(file_get_contents($address),true);
	$user["privacy"]="$privacy";
	$user = json_encode($user,true);
	file_put_contents($address,$user);
}
function get_privacy($address){
	if(file_exists($address)){
		$user = json_decode(file_get_contents($address),true);
		return isset($user["privacy"]) ? $user["privacy"] : "active";
	}
	return "active";
}
function set_Whisper_setting($address,$setting,$value){
	$user = json_decode(file_get_contents($address),true);
	if(!isset($user["Whisper_settings"])){
		$user["Whisper_settings"] = [];
	}
	$user["Whisper_settings"][$setting] = $value;
	$user = json_encode($user,true);
	file_put_contents($address,$user);
}
function mark_message_viewed($address, $message_id){
	$user = json_decode(file_get_contents($address),true);
	if(!isset($user["viewed_messages"])){
		$user["viewed_messages"] = [];
	}
	$user["viewed_messages"][$message_id] = time();
	$user = json_encode($user,true);
	file_put_contents($address,$user);
}
function is_message_viewed($address, $message_id){
	if(file_exists($address)){
		$user = json_decode(file_get_contents($address),true);
		return isset($user["viewed_messages"][$message_id]);
	}
	return false;
}
function get_Whisper_setting($address,$setting){
	if(file_exists($address)){
		$user = json_decode(file_get_contents($address),true);
		if(isset($user["Whisper_settings"][$setting])){
			return $user["Whisper_settings"][$setting];
		}
	}
	// پیش‌فرض: اعلان‌ها فعال، بقیه غیرفعال
	$defaults = [
		'read_notification' => 'active',
		'receive_notification' => 'active',
		'anonymous_Whisper' => 'inactive',
		'disposable_Whisper' => 'inactive'
	];
	return isset($defaults[$setting]) ? $defaults[$setting] : 'inactive';
}
function get_text($key, $lang, $replacements = []){
	global $texts;
	$text = isset($texts[$lang][$key]) ? $texts[$lang][$key] : $texts['fa'][$key];
	foreach($replacements as $search => $replace){
		$text = str_replace('{'.$search.'}', $replace, $text);
	}
	return $text;
}
function is_admin($from_id){
	if($from_id == $Admin){
		return 1;
	}else{
		return 0;
	}
}
//============================================
$update = json_decode(file_get_contents('php://input'));
$message = $update->message; 
$chat_id = $message->chat->id;
$from_id = $message->from->id;
$first_name = $message->from->first_name;
$text = $message->text;
$message_id = $message->message_id;  
$inline = $update->inline_query;
$inline_id = $update->inline_query->id;
$inline_text = $update->inline_query->query;
$inline_from = $update->inline_query->from->id;
$inline_name = $update->inline_query->from->first_name;
$forward_name = $message->forward_from->first_name;
$forward_from = $message->forward_from;
$forward_id = $forward_from->id;
$forward_text = $forward_from->message;
$forward_username = $forward_from->username;
$data = $update->callback_query->data;
$messageid = isset($update->callback_query->message) ? $update->callback_query->message->message_id : null;
$chatid = isset($update->callback_query->message) ? $update->callback_query->message->chat->id : null;
$inline_message_id = isset($update->callback_query->inline_message_id) ? $update->callback_query->inline_message_id : null;
$fromid = $update->callback_query->from->id;
$firstname = $update->callback_query->from->first_name;
$user = json_decode(file_get_contents("data/$from_id.json"),true);
$step = $user["step"];
$calid = $update->callback_query->id;
$usernameca = $update->callback_query->from->username;
//=============================================
if(!file_exists("data/$from_id.json")){
	if(!is_dir("data")){
		mkdir("data", 0777, true);
	}
	$initial_data = json_encode([
		"step" => "None",
		"language" => "fa",
		"privacy" => "active",
		"Whisper_settings" => [
			"read_notification" => "active",
			"receive_notification" => "active",
			"anonymous_Whisper" => "inactive",
			"disposable_Whisper" => "inactive"
		]
	]);
	file_put_contents("data/$from_id.json", $initial_data);
}
$user_lang = get_language("data/$from_id.json");

if($text == "/start"){
		sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>get_text('welcome', $user_lang, ['name' => $first_name]),
		'parse_mode'=>'HTML',
		'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('private_message', $user_lang),'callback_data'=>'private_msg'], ['text'=>get_text('send_Unknown', $user_lang),'callback_data'=>'send']],
		[['text'=>get_text('support', $user_lang),'callback_data'=>'support'], ['text'=>get_text('help', $user_lang),'callback_data'=>'help']],
		[['text'=>get_text('privacy', $user_lang),'callback_data'=>'privacy'], ['text'=>get_text('language', $user_lang),'callback_data'=>'language']],
		[['text'=>get_text('anonymous_message', $user_lang),'callback_data'=>'anonymous']]
		],
	])
	]);
}
elseif($text == "test"){
	if(is_admin($from_id)){
		sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>"Yeah"
		]);
	}else{
		sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>"Nope"
		]);
	}
}
elseif($data == 'send'){
	$user_lang = get_language("data/$fromid.json");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('Whisper_main', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('guide', $user_lang),'callback_data'=>'Whisper_guide'], ['text'=>get_text('settings', $user_lang),'callback_data'=>'Whisper_settings']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'help'){
	$user_lang = get_language("data/$fromid.json");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('help_text', $user_lang, ['bot' => $UsernameBot]),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('view_example', $user_lang),'switch_inline_query_current_chat'=>'سلام @username']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'language'){
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('select_language', 'fa'), // Always show in both languages
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('english', 'en'),'callback_data'=>'set_lang_en'], ['text'=>get_text('persian', 'fa'),'callback_data'=>'set_lang_fa']],
		[['text'=>get_text('main_menu', get_language("data/$fromid.json")),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'set_lang_fa'){
	set_language("data/$fromid.json", "fa");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('welcome', 'fa', ['name' => $firstname]),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('private_message', 'fa'),'callback_data'=>'private_msg'], ['text'=>get_text('send_Unknown', 'fa'),'callback_data'=>'send']],
		[['text'=>get_text('support', 'fa'),'callback_data'=>'support'], ['text'=>get_text('help', 'fa'),'callback_data'=>'help']],
		[['text'=>get_text('privacy', 'fa'),'callback_data'=>'privacy'], ['text'=>get_text('language', 'fa'),'callback_data'=>'language']],
		[['text'=>get_text('anonymous_message', 'fa'),'callback_data'=>'anonymous']]
		],
	])
	]);
}
elseif($data == 'set_lang_en'){
	set_language("data/$fromid.json", "en");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('welcome', 'en', ['name' => $firstname]),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('private_message', 'en'),'callback_data'=>'private_msg'], ['text'=>get_text('send_Unknown', 'en'),'callback_data'=>'send']],
		[['text'=>get_text('support', 'en'),'callback_data'=>'support'], ['text'=>get_text('help', 'en'),'callback_data'=>'help']],
		[['text'=>get_text('privacy', 'en'),'callback_data'=>'privacy'], ['text'=>get_text('language', 'en'),'callback_data'=>'language']],
		[['text'=>get_text('anonymous_message', 'en'),'callback_data'=>'anonymous']]
		],
	])
	]);
}
elseif($step == 'send' && isset($forward_from)){
		$user_lang = get_language("data/$from_id.json");
		$Result = sheikh('getChatMember',[
		'chat_id'=>$forward_id,
		'user_id'=>$forward_id
		]);
		$ok = $Result->ok;
		if($ok != 1){
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('user_not_found', $user_lang)
		]);
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('welcome', $user_lang, ['name' => $first_name]),
			'parse_mode'=>'HTML',
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('private_message', $user_lang),'callback_data'=>'private_msg'], ['text'=>get_text('send_Unknown', $user_lang),'callback_data'=>'send']],
			[['text'=>get_text('support', $user_lang),'callback_data'=>'support'], ['text'=>get_text('help', $user_lang),'callback_data'=>'help']],
			[['text'=>get_text('privacy', $user_lang),'callback_data'=>'privacy'], ['text'=>get_text('language', $user_lang),'callback_data'=>'language']],
			[['text'=>get_text('anonymous_message', $user_lang),'callback_data'=>'anonymous']]
			],
		])
	]);
	 step("data/$from_id.json","None");
		}else{
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('user_info', $user_lang, [
				'id' => $forward_id,
				'username' => $forward_username,
				'name' => $forward_name
			]),
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('send_Unknown_to', $user_lang, ['name' => $forward_name]),'switch_inline_query'=>"\nپیام شما\n#".$forward_id.""]],
			[['text'=>get_text('main_menu', $user_lang),'callback_data'=>"menu"]]
			],
		])
		]);
		 step("data/$from_id.json","None");

	}
}
elseif(preg_match('/^(\d+)/',$text,$y) && $step == 'send'){
		$user_lang = get_language("data/$from_id.json");
		$Result = sheikh('getChatMember',[
		'chat_id'=>$text,
		'user_id'=>$text
		]);
		$ok = $Result->ok;
		$name = $Result->result->user->first_name;
		$usern = $Result->result->user->username;
		if($ok != 1){
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('user_not_found', $user_lang)
		]);
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('welcome', $user_lang, ['name' => $first_name]),
			'parse_mode'=>'HTML',
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('private_message', $user_lang),'callback_data'=>'private_msg'], ['text'=>get_text('send_Unknown', $user_lang),'callback_data'=>'send']],
			[['text'=>get_text('support', $user_lang),'callback_data'=>'support'], ['text'=>get_text('help', $user_lang),'callback_data'=>'help']],
			[['text'=>get_text('privacy', $user_lang),'callback_data'=>'privacy'], ['text'=>get_text('language', $user_lang),'callback_data'=>'language']],
			[['text'=>get_text('anonymous_message', $user_lang),'callback_data'=>'anonymous']]
			],
		])
	]);
	 step("data/$from_id.json","None");
		}else{
			sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('user_info', $user_lang, [
				'id' => $text,
				'username' => $usern,
				'name' => $name
			]),
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('send_Unknown_to', $user_lang, ['name' => $name]),'switch_inline_query'=>"\nپیام شما\n#".$text.""]],
			[['text'=>get_text('main_menu', $user_lang),'callback_data'=>"menu"]]
			],
		])
		]);
		 step("data/$from_id.json","None");
	}
}
elseif(preg_match('/^@/',$text,$x) && $step == 'send'){
		$user_lang = get_language("data/$from_id.json");
		sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('user_info', $user_lang, [
				'id' => 'N/A',
				'username' => ltrim($text, '@'),
				'name' => $text
			]),
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('send_Unknown_to', $user_lang, ['name' => $text]),'switch_inline_query'=>"\nپیام شما\n".$text.""]],
			[['text'=>get_text('main_menu', $user_lang),'callback_data'=>"menu"]]
			],
		])
		]);
	step("data/$from_id.json","None");
}
elseif(preg_match('/^@/',$text,$x) && $step == 'anonymous'){
	$user_lang = get_language("data/$from_id.json");
	$group_username = $text;

	// بررسی عضویت ربات در گروه و دریافت اطلاعات گروه
	$ChatResult = sheikh('getChat',[
		'chat_id'=>$group_username
	]);

	if($ChatResult->ok){
		$group_title = $ChatResult->result->title;

		// بررسی عضویت ربات در گروه
		$MemberResult = sheikh('getChatMember',[
			'chat_id'=>$group_username,
			'user_id'=>$Admin // بررسی با ID ادمین یا ID ربات
		]);

		if($MemberResult->ok){
			// گروه پیدا شد و ربات عضو است
			sheikh('sendmessage',[
				'chat_id'=>$chat_id,
				'text'=>get_text('group_found', $user_lang, ['group' => $group_username, 'group_title' => $group_title]),
				'reply_markup'=>json_encode([
				'inline_keyboard'=>[
				[['text'=>get_text('cancel', $user_lang),'callback_data'=>'cancel']]
				],
			])
			]);

			// ذخیره یوزرنیم و نام گروه
			$user = json_decode(file_get_contents("data/$from_id.json"),true);
			$user["group_username"]="$group_username";
			$user["group_title"]="$group_title";
			$user["step"]="anonymous_message";
			$user = json_encode($user,true);
			file_put_contents("data/$from_id.json",$user);
		} else {
			// ربات عضو گروه نیست
			sheikh('sendmessage',[
				'chat_id'=>$chat_id,
				'text'=>get_text('group_not_found', $user_lang),
				'reply_markup'=>json_encode([
				'inline_keyboard'=>[
				[['text'=>get_text('cancel', $user_lang),'callback_data'=>'cancel']]
				],
			])
			]);
		}
	} else {
		// گروه پیدا نشد یا ربات عضو نیست
		sheikh('sendmessage',[
			'chat_id'=>$chat_id,
			'text'=>get_text('group_not_found', $user_lang),
			'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('cancel', $user_lang),'callback_data'=>'cancel']]
			],
		])
		]);
	}
}
elseif($step == 'anonymous_message' && !empty($text) && $text != '/start'){
	$user_lang = get_language("data/$from_id.json");

	// ذخیره پیام
	$user = json_decode(file_get_contents("data/$from_id.json"),true);
	$user["anonymous_text"]="$text";
	$user["step"]="anonymous_target";
	$user = json_encode($user,true);
	file_put_contents("data/$from_id.json",$user);

	sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>get_text('message_received', $user_lang),
		'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('send_to_all', $user_lang),'callback_data'=>'send_all'], ['text'=>get_text('send_to_specific', $user_lang),'callback_data'=>'send_specific']],
		[['text'=>get_text('cancel', $user_lang),'callback_data'=>'cancel']]
		],
	])
	]);
}
elseif($data == 'cancel'){
	$user_lang = get_language("data/$fromid.json");
	step("data/$fromid.json","None");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('welcome', $user_lang, ['name' => $firstname]),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('private_message', $user_lang),'callback_data'=>'private_msg'], ['text'=>get_text('send_Unknown', $user_lang),'callback_data'=>'send']],
		[['text'=>get_text('support', $user_lang),'callback_data'=>'support'], ['text'=>get_text('help', $user_lang),'callback_data'=>'help']],
		[['text'=>get_text('privacy', $user_lang),'callback_data'=>'privacy'], ['text'=>get_text('language', $user_lang),'callback_data'=>'language']],
		[['text'=>get_text('anonymous_message', $user_lang),'callback_data'=>'anonymous']]
		],
	])
	]);
}
elseif($data == 'menu'){
	$user_lang = get_language("data/$fromid.json");
	step("data/$fromid.json","None");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('welcome', $user_lang, ['name' => $firstname]),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('private_message', $user_lang),'callback_data'=>'private_msg'], ['text'=>get_text('send_Unknown', $user_lang),'callback_data'=>'send']],
		[['text'=>get_text('support', $user_lang),'callback_data'=>'support'], ['text'=>get_text('help', $user_lang),'callback_data'=>'help']],
		[['text'=>get_text('privacy', $user_lang),'callback_data'=>'privacy'], ['text'=>get_text('language', $user_lang),'callback_data'=>'language']],
		[['text'=>get_text('anonymous_message', $user_lang),'callback_data'=>'anonymous']]
		],
	])
	]);
}
elseif($data == 'private_msg'){
	$user_lang = get_language("data/$fromid.json");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('private_instruction', $user_lang, ['name' => $firstname]),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('cancel', $user_lang),'callback_data'=>'cancel']]
		],
	])
	]);
	step("data/$fromid.json","send");
}
elseif($data == 'Whisper_guide'){
	$user_lang = get_language("data/$fromid.json");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('help_text', $user_lang, ['bot' => $UsernameBot]),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('view_example', $user_lang),'switch_inline_query_current_chat'=>'سلام @username']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'Whisper_settings'){
	$user_lang = get_language("data/$fromid.json");

	// دریافت وضعیت تنظیمات
	$read_notif = get_Whisper_setting("data/$fromid.json", 'read_notification');
	$receive_notif = get_Whisper_setting("data/$fromid.json", 'receive_notification');
	$anonymous = get_Whisper_setting("data/$fromid.json", 'anonymous_Whisper');
	$disposable = get_Whisper_setting("data/$fromid.json", 'disposable_Whisper');

	// تولید متن دکمه‌ها با وضعیت
	$read_status = $read_notif == 'active' ? '✅' : '❌';
	$receive_status = $receive_notif == 'active' ? '✅' : '❌';
	$anonymous_status = $anonymous == 'active' ? '✅' : '❌';
	$disposable_status = $disposable == 'active' ? '✅' : '❌';

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('Whisper_settings_text', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('read_notification', $user_lang).' ('.$read_status.')','callback_data'=>'toggle_read_notif'], ['text'=>get_text('receive_notification', $user_lang).' ('.$receive_status.')','callback_data'=>'toggle_receive_notif']],
		[['text'=>get_text('anonymous_Whisper', $user_lang).' ('.$anonymous_status.')','callback_data'=>'toggle_anonymous'], ['text'=>get_text('disposable_Whisper', $user_lang).' ('.$disposable_status.')','callback_data'=>'toggle_disposable']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'toggle_read_notif'){
	$user_lang = get_language("data/$fromid.json");
	$current_status = get_Whisper_setting("data/$fromid.json", 'read_notification');
	$new_status = $current_status == 'active' ? 'inactive' : 'active';
	set_Whisper_setting("data/$fromid.json", 'read_notification', $new_status);

	// بازگشت به صفحه تنظیمات با وضعیت جدید
	$read_notif = get_Whisper_setting("data/$fromid.json", 'read_notification');
	$receive_notif = get_Whisper_setting("data/$fromid.json", 'receive_notification');
	$anonymous = get_Whisper_setting("data/$fromid.json", 'anonymous_Whisper');
	$disposable = get_Whisper_setting("data/$fromid.json", 'disposable_Whisper');

	$read_status = $read_notif == 'active' ? '✅' : '❌';
	$receive_status = $receive_notif == 'active' ? '✅' : '❌';
	$anonymous_status = $anonymous == 'active' ? '✅' : '❌';
	$disposable_status = $disposable == 'active' ? '✅' : '❌';

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('Whisper_settings_text', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('read_notification', $user_lang).' ('.$read_status.')','callback_data'=>'toggle_read_notif'], ['text'=>get_text('receive_notification', $user_lang).' ('.$receive_status.')','callback_data'=>'toggle_receive_notif']],
		[['text'=>get_text('anonymous_Whisper', $user_lang).' ('.$anonymous_status.')','callback_data'=>'toggle_anonymous'], ['text'=>get_text('disposable_Whisper', $user_lang).' ('.$disposable_status.')','callback_data'=>'toggle_disposable']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'toggle_receive_notif'){
	$user_lang = get_language("data/$fromid.json");
	$current_status = get_Whisper_setting("data/$fromid.json", 'receive_notification');
	$new_status = $current_status == 'active' ? 'inactive' : 'active';
	set_Whisper_setting("data/$fromid.json", 'receive_notification', $new_status);

	// بازگشت به صفحه تنظیمات با وضعیت جدید
	$read_notif = get_Whisper_setting("data/$fromid.json", 'read_notification');
	$receive_notif = get_Whisper_setting("data/$fromid.json", 'receive_notification');
	$anonymous = get_Whisper_setting("data/$fromid.json", 'anonymous_Whisper');
	$disposable = get_Whisper_setting("data/$fromid.json", 'disposable_Whisper');

	$read_status = $read_notif == 'active' ? '✅' : '❌';
	$receive_status = $receive_notif == 'active' ? '✅' : '❌';
	$anonymous_status = $anonymous == 'active' ? '✅' : '❌';
	$disposable_status = $disposable == 'active' ? '✅' : '❌';

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('Whisper_settings_text', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('read_notification', $user_lang).' ('.$read_status.')','callback_data'=>'toggle_read_notif'], ['text'=>get_text('receive_notification', $user_lang).' ('.$receive_status.')','callback_data'=>'toggle_receive_notif']],
		[['text'=>get_text('anonymous_Whisper', $user_lang).' ('.$anonymous_status.')','callback_data'=>'toggle_anonymous'], ['text'=>get_text('disposable_Whisper', $user_lang).' ('.$disposable_status.')','callback_data'=>'toggle_disposable']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'toggle_anonymous'){
	$user_lang = get_language("data/$fromid.json");
	$current_status = get_Whisper_setting("data/$fromid.json", 'anonymous_Whisper');
	$new_status = $current_status == 'active' ? 'inactive' : 'active';
	set_Whisper_setting("data/$fromid.json", 'anonymous_Whisper', $new_status);

	// بازگشت به صفحه تنظیمات با وضعیت جدید
	$read_notif = get_Whisper_setting("data/$fromid.json", 'read_notification');
	$receive_notif = get_Whisper_setting("data/$fromid.json", 'receive_notification');
	$anonymous = get_Whisper_setting("data/$fromid.json", 'anonymous_Whisper');
	$disposable = get_Whisper_setting("data/$fromid.json", 'disposable_Whisper');

	$read_status = $read_notif == 'active' ? '✅' : '❌';
	$receive_status = $receive_notif == 'active' ? '✅' : '❌';
	$anonymous_status = $anonymous == 'active' ? '✅' : '❌';
	$disposable_status = $disposable == 'active' ? '✅' : '❌';

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('Whisper_settings_text', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('read_notification', $user_lang).' ('.$read_status.')','callback_data'=>'toggle_read_notif'], ['text'=>get_text('receive_notification', $user_lang).' ('.$receive_status.')','callback_data'=>'toggle_receive_notif']],
		[['text'=>get_text('anonymous_Whisper', $user_lang).' ('.$anonymous_status.')','callback_data'=>'toggle_anonymous'], ['text'=>get_text('disposable_Whisper', $user_lang).' ('.$disposable_status.')','callback_data'=>'toggle_disposable']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'toggle_disposable'){
	$user_lang = get_language("data/$fromid.json");
	$current_status = get_Whisper_setting("data/$fromid.json", 'disposable_Whisper');
	$new_status = $current_status == 'active' ? 'inactive' : 'active';
	set_Whisper_setting("data/$fromid.json", 'disposable_Whisper', $new_status);

	// بازگشت به صفحه تنظیمات با وضعیت جدید
	$read_notif = get_Whisper_setting("data/$fromid.json", 'read_notification');
	$receive_notif = get_Whisper_setting("data/$fromid.json", 'receive_notification');
	$anonymous = get_Whisper_setting("data/$fromid.json", 'anonymous_Whisper');
	$disposable = get_Whisper_setting("data/$fromid.json", 'disposable_Whisper');

	$read_status = $read_notif == 'active' ? '✅' : '❌';
	$receive_status = $receive_notif == 'active' ? '✅' : '❌';
	$anonymous_status = $anonymous == 'active' ? '✅' : '❌';
	$disposable_status = $disposable == 'active' ? '✅' : '❌';

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('Whisper_settings_text', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('read_notification', $user_lang).' ('.$read_status.')','callback_data'=>'toggle_read_notif'], ['text'=>get_text('receive_notification', $user_lang).' ('.$receive_status.')','callback_data'=>'toggle_receive_notif']],
		[['text'=>get_text('anonymous_Whisper', $user_lang).' ('.$anonymous_status.')','callback_data'=>'toggle_anonymous'], ['text'=>get_text('disposable_Whisper', $user_lang).' ('.$disposable_status.')','callback_data'=>'toggle_disposable']],
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'support'){
	$user_lang = get_language("data/$fromid.json");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('support_text', $user_lang),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'privacy'){
	$user_lang = get_language("data/$fromid.json");
	$privacy_status = get_privacy("data/$fromid.json");
	$privacy_text = $privacy_status == 'active' ? 'privacy_text_active' : 'privacy_text_inactive';
	$privacy_button = $privacy_status == 'active' ? 'disable_privacy' : 'enable_privacy';
	$privacy_action = $privacy_status == 'active' ? 'disable_privacy' : 'enable_privacy';

	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text($privacy_text, $user_lang),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text($privacy_button, $user_lang),'callback_data'=>$privacy_action], ['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'enable_privacy'){
	$user_lang = get_language("data/$fromid.json");
	set_privacy("data/$fromid.json", "active");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('privacy_text_active', $user_lang),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('disable_privacy', $user_lang),'callback_data'=>'disable_privacy'], ['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'disable_privacy'){
	$user_lang = get_language("data/$fromid.json");
	set_privacy("data/$fromid.json", "inactive");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('privacy_text_inactive', $user_lang),
	'parse_mode'=>'HTML',
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('enable_privacy', $user_lang),'callback_data'=>'enable_privacy'], ['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
		],
	])
	]);
}
elseif($data == 'anonymous'){
	$user_lang = get_language("data/$fromid.json");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('anonymous_text', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('cancel', $user_lang),'callback_data'=>'cancel']]
		],
	])
	]);
	step("data/$fromid.json","anonymous");
}
elseif($data == 'send_all'){
	$user_lang = get_language("data/$fromid.json");
	$user = json_decode(file_get_contents("data/$fromid.json"),true);
	$group_username = $user["group_username"];
	$anonymous_text = $user["anonymous_text"];

	// رمزگذاری پیام
	$encoded_text = base64_encode($anonymous_text);

	// ارسال پیام به گروه برای همه
	$message_text = get_text('anonymous_group_all', $user_lang);
	$send_result = sheikh('sendMessage',[
		'chat_id'=>$group_username,
		'text'=>$message_text,
		'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('view_message', $user_lang),'callback_data'=>'view_anon_all_'.$encoded_text]]
			],
		])
	]);

	if($send_result->ok){
		sheikh('editMessageText',[
		'chat_id'=>$chatid,
		'message_id'=>$messageid,
		'text'=>get_text('message_sent', $user_lang),
		'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
			],
		])
		]);
	} else {
		sheikh('editMessageText',[
		'chat_id'=>$chatid,
		'message_id'=>$messageid,
		'text'=>get_text('send_error', $user_lang),
		'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
			],
		])
		]);
	}
	step("data/$fromid.json","None");
}
elseif($data == 'send_specific'){
	$user_lang = get_language("data/$fromid.json");
	sheikh('editMessageText',[
	'chat_id'=>$chatid,
	'message_id'=>$messageid,
	'text'=>get_text('enter_username', $user_lang),
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
		[['text'=>get_text('cancel', $user_lang),'callback_data'=>'cancel']]
		],
	])
	]);
	step("data/$fromid.json","anonymous_specific");
}
elseif($step == 'anonymous_specific' && preg_match('/^@/',$text,$x)){
	$user_lang = get_language("data/$from_id.json");
	$user = json_decode(file_get_contents("data/$from_id.json"),true);
	$group_username = $user["group_username"];
	$anonymous_text = $user["anonymous_text"];
	$target_user = $text;

	// رمزگذاری پیام و یوزرنیم
	$encoded_text = base64_encode($anonymous_text);
	$encoded_user = base64_encode($target_user);

	// ارسال پیام به گروه برای فرد خاص
	$message_text = get_text('anonymous_group_message', $user_lang, ['target' => $target_user]);
	$send_result = sheikh('sendMessage',[
		'chat_id'=>$group_username,
		'text'=>$message_text,
		'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('view_message', $user_lang),'callback_data'=>'view_anon_'.$encoded_user.'_'.$encoded_text]]
			],
		])
	]);

	if($send_result->ok){
		sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>get_text('message_sent', $user_lang),
		'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
			],
		])
		]);
	} else {
		sheikh('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>get_text('send_error', $user_lang),
		'reply_markup'=>json_encode([
			'inline_keyboard'=>[
			[['text'=>get_text('main_menu', $user_lang),'callback_data'=>'menu']]
			],
		])
		]);
	}
	step("data/$from_id.json","None");
}
elseif(!is_null($inline) && (empty($inline_text) || trim($inline_text) == "")){
	// وقتی کاربر فقط یوزرنیم ربات را تایپ کرده
	$user_lang = get_language("data/$inline_from.json");

	sheikh('answerInlineQuery', [
        'inline_query_id' =>$inline_id,
		'is_personal' =>true,
		'cache_time' =>"1",
        'results' => json_encode([
        	[
	        'type' => 'article',
	        'thumb_url'=>"https://cdn-icons-png.flaticon.com/512/3135/3135715.png",
	        'id' =>'ads_'.rand(1,999999),
	        'title' => get_text('ads', $user_lang),
			'description' => "برای تبلیغات در ربات با ادمین تماس بگیرید",
			'input_message_content' => [ 'message_text' => get_text('ads_text', $user_lang)]
	        ],
	        [
	        'type' => 'article',
	        'thumb_url'=>"https://cdn-icons-png.flaticon.com/512/3002/3002543.png",
	        'id' =>'guide_'.rand(1,999999),
	        'title' => get_text('guide', $user_lang),
			'description' => "راهنمای کامل استفاده از ربات",
			'input_message_content' => [ 'message_text' => get_text('guide_text', $user_lang)]
	        ]
        ])
	]);
}
elseif(!is_null($inline) && strstr($inline_text,"@")){
	$u = explode("@", $inline_text);
	$user = "@".$u[1]."";
	$txt = $u[0];
	$Tex = base64_encode($txt);
	$user_lang = get_language("data/$inline_from.json");

	// بررسی تنظیمات فرستنده
	$anonymous_setting = get_Whisper_setting("data/$inline_from.json", 'anonymous_Whisper');
	$disposable_setting = get_Whisper_setting("data/$inline_from.json", 'disposable_Whisper');
	$receive_notif = get_Whisper_setting("data/$inline_from.json", 'receive_notification');

	// تولید callback data بر اساس تنظیمات
	$callback_prefix = "show2";
	if($disposable_setting == 'active') {
		$callback_prefix = "show2_disposable";
	}

	// تولید پیام بر اساس تنظیمات
	if($anonymous_setting == 'active') {
		// برای نجوا بی نام از متن خاص استفاده می‌کنیم
		$message_text = get_text('anonymous_Whisper_message', $user_lang, ['sender' => $inline_name]);
	} else {
		// برای نجوا معمولی
		$sender_name = $inline_name;
		$message_text = get_text('you_have_message', $user_lang, ['user' => $user, 'sender' => $sender_name]);
	}

	// حذف اعلان دریافت - فقط اعلان خوانده شدن باقی می‌ماند

	sheikh('answerInlineQuery', [
        'inline_query_id' =>$inline_id,
		'is_personal' =>true,
		'cache_time' =>"1",
        'results' => json_encode([[
        'type' => 'article',
        'thumb_url'=>"https://telegra.ph/file/f3a4bfdaafaa26db2681d.jpg",
        'id' =>base64_encode(rand(1,999999)),
        'title' => get_text('send_Unknown_here', $user_lang),
		'description' => get_text('send_secret_to', $user_lang, ['user' => $user]),
		'input_message_content' => [ 'message_text' => $message_text],
		'reply_markup'=>([
		'inline_keyboard'=>[
		[['text'=>get_text('show_message', $user_lang),'callback_data'=>$callback_prefix."_".$user."_".$Tex."_".$inline_from]]
	]
    ])
	]])
	]);
}
elseif(isset($inline) && strstr($inline_text,"#")){
	$k = explode("#", $inline_text);
	$id = $k[1];
	$txt = $k[0];
	$Tex = base64_encode($txt);
	$Result = sheikh('getChatMember',['chat_id'=>$id,'user_id'=>$id]);
	$name = $Result->result->user->first_name;
	$usern = $Result->result->user->username;
	$user_lang = get_language("data/$inline_from.json");

	// بررسی تنظیمات فرستنده
	$anonymous_setting = get_Whisper_setting("data/$inline_from.json", 'anonymous_Whisper');
	$disposable_setting = get_Whisper_setting("data/$inline_from.json", 'disposable_Whisper');
	$receive_notif = get_Whisper_setting("data/$inline_from.json", 'receive_notification');

	// تولید callback data بر اساس تنظیمات
	$callback_prefix = "show";
	if($disposable_setting == 'active') {
		$callback_prefix = "show_disposable";
	}

	// تولید پیام بر اساس تنظیمات
	if($anonymous_setting == 'active') {
		// برای نجوا بی نام از متن خاص استفاده می‌کنیم
		$message_text = get_text('anonymous_Whisper_message', $user_lang, ['sender' => $inline_name]);
	} else {
		// برای نجوا معمولی
		$sender_name = $inline_name;
		$message_text = get_text('you_have_message', $user_lang, ['user' => $name . " | @" . $usern, 'sender' => $sender_name]);
	}

	// حذف اعلان دریافت - فقط اعلان خوانده شدن باقی می‌ماند

		sheikh('answerInlineQuery', [
        'inline_query_id' =>$inline_id,
		'is_personal' =>true,
		'cache_time' =>"1",
        'results' => json_encode([[
        'type' => 'article',
        'thumb_url'=>"https://telegra.ph/file/f3a4bfdaafaa26db2681d.jpg",
        'id' =>base64_encode(rand(1,999999)),
        'title' => get_text('send_Unknown_here', $user_lang),
		'description' => get_text('send_secret_to_user', $user_lang, ['name' => $name]),
		'input_message_content' => [ 'message_text' => $message_text],
		'reply_markup'=>([
		'inline_keyboard'=>[
		[['text'=>get_text('show_message', $user_lang),'callback_data'=>$callback_prefix."_".$id."_".$Tex."_".$inline_from]]
	]
    ])
	]])
	]);
}
elseif(preg_match('/^show_(\d+)_(.*)_(.*)/',$data,$nop)){
	$id = $nop[1];
	$txt = $nop[2];
	$sender_id = $nop[3];
	$text = base64_decode($txt);
	$user_lang = get_language("data/$fromid.json");
	if($fromid == $id){
		// دریافت اطلاعات فرستنده و گیرنده برای ادیت پیام
		$sender_result = sheikh('getChatMember',['chat_id'=>$sender_id,'user_id'=>$sender_id]);
		$sender_name = $sender_result->result->user->first_name ?? 'ناشناس';
		$sender_username = $sender_result->result->user->username ?? '';
		$sender_display = !empty($sender_username) ? "@$sender_username" : $sender_name;

		$receiver_result = sheikh('getChatMember',['chat_id'=>$id,'user_id'=>$id]);
		$receiver_name = $receiver_result->result->user->first_name ?? 'کاربر';
		$receiver_username = $receiver_result->result->user->username ?? '';
		$receiver_display = !empty($receiver_username) ? "@$receiver_username" : $receiver_name;

		$reader_username = !empty($usernameca) ? "@$usernameca" : $firstname;
		$user_lang = get_language("data/$sender_id.json");

		// بررسی تنظیمات نجوا بی نام فرستنده
		$sender_anonymous_setting = get_Whisper_setting("data/$sender_id.json", 'anonymous_Whisper');

		// ادیت کردن پیام اصلی
		if($sender_anonymous_setting == 'active') {
			$updated_message = get_text('message_read_by_anonymous', $user_lang, [
				'sender' => $sender_name
			]);
		} else {
			$updated_message = get_text('message_read_by', $user_lang, [
				'user' => $receiver_display,
				'sender' => $sender_name,
				'reader' => $reader_username
			]);
		}

		error_log("DEBUG show_: Trying to edit message - chatid=$chatid, messageid=$messageid, inline_message_id=$inline_message_id");
		error_log("DEBUG show_: Updated message: $updated_message");

		// تولید دکمه‌های جدید
		$new_buttons = [
			[
				['text'=>'💬 پاسخ','switch_inline_query_current_chat'=>"پیام ".$sender_display],
				['text'=>get_text('show_message', $user_lang),'callback_data'=>"reshow_".$id."_".$txt."_".$sender_id]
			]
		];

		// بررسی نوع پیام (inline یا معمولی)
		if(!empty($inline_message_id)) {
			// پیام inline است
			$edit_result = sheikh('editMessageText',[
				'inline_message_id'=>$inline_message_id,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show_: Inline edit result: " . json_encode($edit_result));
		} elseif(!empty($messageid) && !empty($chatid)) {
			// پیام معمولی است
			$edit_result = sheikh('editMessageText',[
				'chat_id'=>$chatid,
				'message_id'=>$messageid,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show_: Regular edit result: " . json_encode($edit_result));
		} else {
			error_log("DEBUG show_: ERROR - Neither inline_message_id nor messageid/chatid available!");
		}

		// بررسی اعلان خوانده شدن
		$read_notif = get_Whisper_setting("data/$sender_id.json", 'read_notification');
		if($read_notif == 'active'){
			// ارسال اعلان خوانده شدن به فرستنده
			$reader_name = $firstname;
			$sender_lang = get_language("data/$sender_id.json");
			sheikh('sendMessage',[
				'chat_id'=>$sender_id,
				'text'=>"📖 نجوای شما توسط $reader_name خوانده شد.",
				'reply_markup'=>json_encode([
					'inline_keyboard'=>[
						[
							['text'=>get_text('main_menu', $sender_lang),'callback_data'=>'menu']
						]
					]
				])
			]);
		}

		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "$text",
            'show_alert' =>true
        ]);
	}else{
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => get_text('not_your_message', $user_lang),
            'show_alert' =>true
		]);
	}
}
elseif(preg_match('/^reshow_(\d+)_(.*)_(.*)/',$data,$reshow_match)){
	$id = $reshow_match[1];
	$txt = $reshow_match[2];
	$sender_id = $reshow_match[3];
	$text = base64_decode($txt);
	$user_lang = get_language("data/$fromid.json");

	if($fromid == $id){
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "$text",
            'show_alert' =>true
        ]);
	}else{
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => get_text('not_your_message', $user_lang),
            'show_alert' =>true
		]);
	}
}
elseif(preg_match('/^reshow_disposable_(\d+)_(.*)_(.*)/',$data,$reshow_disp_match)){
	$id = $reshow_disp_match[1];
	$txt = $reshow_disp_match[2];
	$sender_id = $reshow_disp_match[3];
	$user_lang = get_language("data/$fromid.json");

	// برای نجواهای یکبار مصرف، اجازه مشاهده مجدد نمی‌دهیم
	sheikh('answercallbackquery', [
        'callback_query_id' =>$calid,
        'text' => get_text('message_already_viewed', $user_lang),
        'show_alert' =>true
    ]);
}
elseif(preg_match('/^reshow2_disposable_(.*)_(.*)_(.*)/',$data,$reshow2_disp_match)){
	$us = $reshow2_disp_match[1];
	$user = strtolower($us);
	$txt = $reshow2_disp_match[2];
	$sender_id = $reshow2_disp_match[3];
	$user_lang = get_language("data/$fromid.json");

	// برای نجواهای یکبار مصرف، اجازه مشاهده مجدد نمی‌دهیم
	sheikh('answercallbackquery', [
        'callback_query_id' =>$calid,
        'text' => get_text('message_already_viewed', $user_lang),
        'show_alert' =>true
    ]);
}
elseif(preg_match('/^show2_disposable_(.*)_(.*)_(.*)/',$data,$disp_match2)){
	$us = $disp_match2[1];
	$user = strtolower($us);
	$txt = $disp_match2[2];
	$sender_id = $disp_match2[3];
	$text = base64_decode($txt);

	// بررسی یوزرنیم کاربر فعلی
	$current_username = "";
	if(isset($usernameca) && !empty($usernameca)){
		$current_username = "@" . strtolower($usernameca);
	}
	$user_lang = get_language("data/$fromid.json");



	if($current_username == $user){
		// تولید شناسه منحصر به فرد برای این پیام (شامل sender_id و inline_message_id)
		$message_unique_id = md5($data . $messageid . $chatid . $sender_id . $inline_message_id);

		error_log("DEBUG show2_disposable: Generated unique_id=$message_unique_id for data=$data, sender_id=$sender_id");

		// بررسی اینکه آیا کاربر قبلاً این پیام را دیده است
		if(is_message_viewed("data/$fromid.json", $message_unique_id)){
			sheikh('answercallbackquery', [
	            'callback_query_id' =>$calid,
	            'text' => get_text('message_already_viewed', $user_lang),
	            'show_alert' =>true
	        ]);
	        return;
		}

		// علامت‌گذاری پیام به عنوان مشاهده شده
		mark_message_viewed("data/$fromid.json", $message_unique_id);

		// دریافت اطلاعات فرستنده برای ادیت پیام
		$sender_result = sheikh('getChatMember',['chat_id'=>$sender_id,'user_id'=>$sender_id]);
		$sender_name = $sender_result->result->user->first_name ?? 'ناشناس';
		$sender_username = $sender_result->result->user->username ?? '';
		$sender_display = !empty($sender_username) ? "@$sender_username" : $sender_name;

		$reader_username = !empty($usernameca) ? "@$usernameca" : $firstname;
		$user_lang_sender = get_language("data/$sender_id.json");

		// بررسی تنظیمات نجوا بی نام فرستنده
		$sender_anonymous_setting = get_Whisper_setting("data/$sender_id.json", 'anonymous_Whisper');

		// ادیت کردن پیام اصلی
		if($sender_anonymous_setting == 'active') {
			$updated_message = get_text('message_read_by_anonymous', $user_lang_sender, [
				'sender' => $sender_name
			]);
		} else {
			$updated_message = get_text('message_read_by', $user_lang_sender, [
				'user' => $user,
				'sender' => $sender_name,
				'reader' => $reader_username
			]);
		}

		// تولید دکمه‌های جدید
		$new_buttons = [
			[
				['text'=>'💬 پاسخ','switch_inline_query_current_chat'=>"پیام ".$sender_display],
				['text'=>get_text('show_message', $user_lang_sender),'callback_data'=>"reshow2_disposable_".$user."_".$txt."_".$sender_id]
			]
		];

		error_log("DEBUG show2_disposable: Trying to edit message - chatid=$chatid, messageid=$messageid, inline_message_id=$inline_message_id");
		error_log("DEBUG show2_disposable: Updated message: $updated_message");

		// بررسی نوع پیام (inline یا معمولی)
		if(!empty($inline_message_id)) {
			// پیام inline است
			$edit_result = sheikh('editMessageText',[
				'inline_message_id'=>$inline_message_id,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show2_disposable: Inline edit result: " . json_encode($edit_result));
		} elseif(!empty($messageid) && !empty($chatid)) {
			// پیام معمولی است
			$edit_result = sheikh('editMessageText',[
				'chat_id'=>$chatid,
				'message_id'=>$messageid,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show2_disposable: Regular edit result: " . json_encode($edit_result));
		} else {
			error_log("DEBUG show2_disposable: ERROR - Neither inline_message_id nor messageid/chatid available!");
		}

		// بررسی اعلان خوانده شدن
		$read_notif = get_Whisper_setting("data/$sender_id.json", 'read_notification');
		if($read_notif == 'active'){
			// ارسال اعلان خوانده شدن به فرستنده
			$reader_name = $firstname;
			$sender_lang = get_language("data/$sender_id.json");
			sheikh('sendMessage',[
				'chat_id'=>$sender_id,
				'text'=>"📖 نجوای شما توسط $reader_name خوانده شد.",
				'reply_markup'=>json_encode([
					'inline_keyboard'=>[
						[
							['text'=>get_text('main_menu', $sender_lang),'callback_data'=>'menu']
						]
					]
				])
			]);
		}

		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => $text,
            'show_alert' =>true
        ]);
	} else {
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => get_text('not_your_message', $user_lang),
            'show_alert' =>true
		]);
	}
}
elseif(preg_match('/^reshow2_(.*)_(.*)_(.*)/',$data,$reshow2_match)){
	$us = $reshow2_match[1];
	$user = strtolower($us);
	$txt = $reshow2_match[2];
	$sender_id = $reshow2_match[3];
	$text = base64_decode($txt);

	// بررسی یوزرنیم کاربر فعلی
	$current_username = "";
	if(isset($usernameca) && !empty($usernameca)){
		$current_username = "@" . strtolower($usernameca);
	}
	$user_lang = get_language("data/$fromid.json");

	if($current_username == $user){
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "$text",
            'show_alert' =>true
        ]);
	}else{
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => get_text('not_your_message', $user_lang),
            'show_alert' =>true
		]);
	}
}
elseif(preg_match('/^show2_(.*)_(.*)_(.*)/',$data,$nop2)){
	$us = $nop2[1];
	$user = strtolower($us);
	$txt = $nop2[2];
	$sender_id = $nop2[3];
	$text = base64_decode($txt);

	// بررسی یوزرنیم کاربر فعلی
	$current_username = "";
	if(isset($usernameca) && !empty($usernameca)){
		$current_username = "@" . strtolower($usernameca);
	}
	$user_lang = get_language("data/$fromid.json");



	if($current_username == $user){
		// دریافت اطلاعات فرستنده برای ادیت پیام
		$sender_result = sheikh('getChatMember',['chat_id'=>$sender_id,'user_id'=>$sender_id]);
		$sender_name = $sender_result->result->user->first_name ?? 'ناشناس';
		$sender_username = $sender_result->result->user->username ?? '';
		$sender_display = !empty($sender_username) ? "@$sender_username" : $sender_name;

		$reader_username = !empty($usernameca) ? "@$usernameca" : $firstname;
		$user_lang_sender = get_language("data/$sender_id.json");

		// بررسی تنظیمات نجوا بی نام فرستنده
		$sender_anonymous_setting = get_Whisper_setting("data/$sender_id.json", 'anonymous_Whisper');

		// ادیت کردن پیام اصلی
		if($sender_anonymous_setting == 'active') {
			$updated_message = get_text('message_read_by_anonymous', $user_lang_sender, [
				'sender' => $sender_name
			]);
		} else {
			$updated_message = get_text('message_read_by', $user_lang_sender, [
				'user' => $user,
				'sender' => $sender_name,
				'reader' => $reader_username
			]);
		}

		// تولید دکمه‌های جدید
		$new_buttons = [
			[
				['text'=>'💬 پاسخ','switch_inline_query_current_chat'=>"پیام ".$sender_display],
				['text'=>get_text('show_message', $user_lang_sender),'callback_data'=>"reshow2_".$user."_".$txt."_".$sender_id]
			]
		];

		error_log("DEBUG show2: Trying to edit message - chatid=$chatid, messageid=$messageid, inline_message_id=$inline_message_id");
		error_log("DEBUG show2: Updated message: $updated_message");

		// بررسی نوع پیام (inline یا معمولی)
		if(!empty($inline_message_id)) {
			// پیام inline است
			$edit_result = sheikh('editMessageText',[
				'inline_message_id'=>$inline_message_id,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show2: Inline edit result: " . json_encode($edit_result));
		} elseif(!empty($messageid) && !empty($chatid)) {
			// پیام معمولی است
			$edit_result = sheikh('editMessageText',[
				'chat_id'=>$chatid,
				'message_id'=>$messageid,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show2: Regular edit result: " . json_encode($edit_result));
		} else {
			error_log("DEBUG show2: ERROR - Neither inline_message_id nor messageid/chatid available!");
		}

		// بررسی اعلان خوانده شدن
		$read_notif = get_Whisper_setting("data/$sender_id.json", 'read_notification');
		if($read_notif == 'active'){
			// ارسال اعلان خوانده شدن به فرستنده
			$reader_name = $firstname;
			$sender_lang = get_language("data/$sender_id.json");
			sheikh('sendMessage',[
				'chat_id'=>$sender_id,
				'text'=>"📖 نجوای شما توسط $reader_name خوانده شد.",
				'reply_markup'=>json_encode([
					'inline_keyboard'=>[
						[
							['text'=>get_text('main_menu', $sender_lang),'callback_data'=>'menu']
						]
					]
				])
			]);
		}

		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => "$text",
            'show_alert' =>true
        ]);
	}else{
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => get_text('not_your_message', $user_lang),
            'show_alert' =>true
		]);
	}
}
elseif(preg_match('/^show_disposable_(\d+)_(.*)_(.*)/',$data,$disp_match)){
	$id = $disp_match[1];
	$txt = $disp_match[2];
	$sender_id = $disp_match[3];
	$text = base64_decode($txt);
	$user_lang = get_language("data/$fromid.json");

	if($fromid == $id){
		// تولید شناسه منحصر به فرد برای این پیام (شامل sender_id و inline_message_id)
		$message_unique_id = md5($data . $messageid . $chatid . $sender_id . $inline_message_id);

		error_log("DEBUG show_disposable: Generated unique_id=$message_unique_id for data=$data, sender_id=$sender_id");

		// بررسی اینکه آیا کاربر قبلاً این پیام را دیده است
		if(is_message_viewed("data/$fromid.json", $message_unique_id)){
			sheikh('answercallbackquery', [
	            'callback_query_id' =>$calid,
	            'text' => get_text('message_already_viewed', $user_lang),
	            'show_alert' =>true
	        ]);
	        return;
		}

		// علامت‌گذاری پیام به عنوان مشاهده شده
		mark_message_viewed("data/$fromid.json", $message_unique_id);

		// دریافت اطلاعات فرستنده و گیرنده برای ادیت پیام
		$sender_result = sheikh('getChatMember',['chat_id'=>$sender_id,'user_id'=>$sender_id]);
		$sender_name = $sender_result->result->user->first_name ?? 'ناشناس';
		$sender_username = $sender_result->result->user->username ?? '';
		$sender_display = !empty($sender_username) ? "@$sender_username" : $sender_name;

		$receiver_result = sheikh('getChatMember',['chat_id'=>$id,'user_id'=>$id]);
		$receiver_name = $receiver_result->result->user->first_name ?? 'کاربر';
		$receiver_username = $receiver_result->result->user->username ?? '';
		$receiver_display = !empty($receiver_username) ? "@$receiver_username" : $receiver_name;

		$reader_username = !empty($usernameca) ? "@$usernameca" : $firstname;
		$user_lang_sender = get_language("data/$sender_id.json");

		// بررسی تنظیمات نجوا بی نام فرستنده
		$sender_anonymous_setting = get_Whisper_setting("data/$sender_id.json", 'anonymous_Whisper');

		// ادیت کردن پیام اصلی
		if($sender_anonymous_setting == 'active') {
			$updated_message = get_text('message_read_by_anonymous', $user_lang_sender, [
				'sender' => $sender_name
			]);
		} else {
			$updated_message = get_text('message_read_by', $user_lang_sender, [
				'user' => $receiver_display,
				'sender' => $sender_name,
				'reader' => $reader_username
			]);
		}

		// تولید دکمه‌های جدید
		$new_buttons = [
			[
				['text'=>'💬 پاسخ','switch_inline_query_current_chat'=>"پیام ".$sender_display],
				['text'=>get_text('show_message', $user_lang_sender),'callback_data'=>"reshow_disposable_".$id."_".$txt."_".$sender_id]
			]
		];

		error_log("DEBUG show_disposable: Trying to edit message - chatid=$chatid, messageid=$messageid, inline_message_id=$inline_message_id");
		error_log("DEBUG show_disposable: Updated message: $updated_message");

		// بررسی نوع پیام (inline یا معمولی)
		if(!empty($inline_message_id)) {
			// پیام inline است
			$edit_result = sheikh('editMessageText',[
				'inline_message_id'=>$inline_message_id,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show_disposable: Inline edit result: " . json_encode($edit_result));
		} elseif(!empty($messageid) && !empty($chatid)) {
			// پیام معمولی است
			$edit_result = sheikh('editMessageText',[
				'chat_id'=>$chatid,
				'message_id'=>$messageid,
				'text'=>$updated_message,
				'reply_markup'=>json_encode([
					'inline_keyboard'=>$new_buttons
				])
			]);
			error_log("DEBUG show_disposable: Regular edit result: " . json_encode($edit_result));
		} else {
			error_log("DEBUG show_disposable: ERROR - Neither inline_message_id nor messageid/chatid available!");
		}

		// بررسی اعلان خوانده شدن
		$read_notif = get_Whisper_setting("data/$sender_id.json", 'read_notification');
		if($read_notif == 'active'){
			// ارسال اعلان خوانده شدن به فرستنده
			$reader_name = $firstname;
			$sender_lang = get_language("data/$sender_id.json");
			sheikh('sendMessage',[
				'chat_id'=>$sender_id,
				'text'=>"📖 نجوای شما توسط $reader_name خوانده شد.",
				'reply_markup'=>json_encode([
					'inline_keyboard'=>[
						[
							['text'=>get_text('main_menu', $sender_lang),'callback_data'=>'menu']
						]
					]
				])
			]);
		}

		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => $text,
            'show_alert' =>true
        ]);
	} else {
		sheikh('answercallbackquery', [
            'callback_query_id' =>$calid,
            'text' => get_text('not_your_message', $user_lang),
            'show_alert' =>true
		]);
	}
}
elseif(preg_match('/^view_anon_all_(.*)/',$data,$anon_match)){
	$encoded_text = $anon_match[1];
	$anonymous_text = base64_decode($encoded_text);
	$user_lang = get_language("data/$fromid.json");

	sheikh('answercallbackquery', [
		'callback_query_id' =>$calid,
		'text' => $anonymous_text,
		'show_alert' =>true
	]);
}
elseif(preg_match('/^view_anon_(.*)_(.*)/',$data,$anon_match2)){
	$encoded_user = $anon_match2[1];
	$encoded_text = $anon_match2[2];
	$target_user = base64_decode($encoded_user);
	$anonymous_text = base64_decode($encoded_text);
	$user_lang = get_language("data/$fromid.json");

	// بررسی اینکه آیا کاربر کلیک کننده همان فرد مورد نظر است یا نه
	$current_user = "@".$usernameca;
	$current_user_lower = strtolower($current_user);
	$target_user_lower = strtolower($target_user);

	if($current_user_lower == $target_user_lower){
		sheikh('answercallbackquery', [
			'callback_query_id' =>$calid,
			'text' => $anonymous_text,
			'show_alert' =>true
		]);
	} else {
		sheikh('answercallbackquery', [
			'callback_query_id' =>$calid,
			'text' => get_text('not_for_you', $user_lang),
			'show_alert' =>true
		]);
	}
}
// The End -- Version 1.1
?>